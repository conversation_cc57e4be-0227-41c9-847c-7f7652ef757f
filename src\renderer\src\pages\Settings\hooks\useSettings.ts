import { useState } from 'react'
import { SettingsState } from '../types'
import { DEFAULT_SETTINGS } from '../constants'

export const useSettings = () => {
  const [settings, setSettings] = useState<SettingsState>(DEFAULT_SETTINGS)

  const updateChatlogWorkDir = (workDir: string) => {
    setSettings(prev => ({ ...prev, chatlogWorkDir: workDir }))
  }

  const updateCurrentAiConfig = (configId: string) => {
    setSettings(prev => ({ ...prev, currentAiConfig: configId }))
  }

  const updateNotifications = (enabled: boolean) => {
    setSettings(prev => ({ ...prev, notifications: enabled }))
  }

  const updateAutoBackup = (enabled: boolean) => {
    setSettings(prev => ({ ...prev, autoBackup: enabled }))
  }

  const updateTheme = (theme: string) => {
    setSettings(prev => ({ ...prev, theme }))
  }

  const addAiConfig = (config: any) => {
    setSettings(prev => ({
      ...prev,
      aiConfigs: [...prev.aiConfigs, config],
      currentAiConfig: prev.currentAiConfig || config.id
    }))
  }

  const removeAiConfig = (configId: string) => {
    setSettings(prev => ({
      ...prev,
      aiConfigs: prev.aiConfigs.filter(c => c.id !== configId),
      currentAiConfig: prev.currentAiConfig === configId
        ? prev.aiConfigs.find(c => c.id !== configId)?.id || ''
        : prev.currentAiConfig
    }))
  }

  const updateAiConfig = (configId: string, field: string, value: string | boolean) => {
    setSettings(prev => ({
      ...prev,
      aiConfigs: prev.aiConfigs.map(config =>
        config.id === configId ? { ...config, [field]: value } : config
      )
    }))
  }

  return {
    settings,
    setSettings,
    updateChatlogWorkDir,
    updateCurrentAiConfig,
    updateNotifications,
    updateAutoBackup,
    updateTheme,
    addAiConfig,
    removeAiConfig,
    updateAiConfig
  }
}
