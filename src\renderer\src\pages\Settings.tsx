import { useState } from 'react'
import { motion } from 'framer-motion'
import { SidebarTrigger } from '@/components/ui/sidebar'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Settings as SettingsIcon, Database, Brain, Shield } from 'lucide-react'
import { useToast } from '../hooks/use-toast'

const Settings = (): React.ReactElement => {
  const { toast } = useToast()
  const [settings, setSettings] = useState({
    chatlogWorkDir: '',
    currentAiConfig: '',
    aiConfigs: [] as Array<{
      id: string
      name: string
      provider: string
      apiKey: string
      model: string
      baseUrl: string
      enabled: boolean
    }>,
    notifications: true,
    autoBackup: true,
    theme: 'warm'
  })

  const [showAddConfig, setShowAddConfig] = useState(false)
  const [newConfig, setNewConfig] = useState({
    name: '',
    provider: 'openai',
    apiKey: '',
    model: '',
    baseUrl: ''
  })

  const providerTemplates = {
    openai: {
      name: 'OpenAI',
      defaultModel: 'gpt-3.5-turbo',
      baseUrl: 'https://api.openai.com/v1',
      requiresBaseUrl: false
    },
    anthropic: {
      name: 'Anthropic (Claude)',
      defaultModel: 'claude-3-sonnet-20240229',
      baseUrl: 'https://api.anthropic.com',
      requiresBaseUrl: false
    },
    google: {
      name: 'Google (Gemini)',
      defaultModel: 'gemini-pro',
      baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
      requiresBaseUrl: false
    },
    deepseek: {
      name: 'DeepSeek',
      defaultModel: 'deepseek-chat',
      baseUrl: 'https://api.deepseek.com',
      requiresBaseUrl: true
    },
    openrouter: {
      name: 'OpenRouter',
      defaultModel: 'openai/gpt-3.5-turbo',
      baseUrl: 'https://openrouter.ai/api/v1',
      requiresBaseUrl: true
    },
    custom: {
      name: '自定义',
      defaultModel: '',
      baseUrl: '',
      requiresBaseUrl: true
    }
  }

  const handleSave = (): void => {
    toast({
      title: '设置已保存',
      description: '你的配置已成功更新。'
    })
  }

  const testConnection = async (type: 'chatlog' | 'ai', configId?: string): Promise<void> => {
    if (type === 'chatlog') {
      toast({
        title: '测试 Chatlog 连接',
        description: '连接测试成功！'
      })
    } else if (type === 'ai' && configId) {
      const config = settings.aiConfigs.find((c) => c.id === configId)
      toast({
        title: `测试 ${config?.name} 服务`,
        description: '连接测试成功！'
      })
    }
  }

  const updateAiConfig = (configId: string, field: string, value: string | boolean): void => {
    setSettings((prev) => ({
      ...prev,
      aiConfigs: prev.aiConfigs.map((config) =>
        config.id === configId ? { ...config, [field]: value } : config
      )
    }))
  }

  const switchCurrentConfig = (configId: string): void => {
    setSettings((prev) => ({
      ...prev,
      currentAiConfig: configId
    }))
    const config = settings.aiConfigs.find((c) => c.id === configId)
    toast({
      title: '切换成功',
      description: `已切换到 ${config?.name}`
    })
  }

  const addAiConfig = (): void => {
    if (!newConfig.name.trim()) {
      toast({
        title: '错误',
        description: '请输入配置名称'
      })
      return
    }

    const template = providerTemplates[newConfig.provider]
    const configId = Date.now().toString()

    const aiConfig = {
      id: configId,
      name: newConfig.name,
      provider: newConfig.provider,
      apiKey: newConfig.apiKey,
      model: newConfig.model || template.defaultModel,
      baseUrl: newConfig.baseUrl || template.baseUrl,
      enabled: true
    }

    setSettings((prev) => ({
      ...prev,
      aiConfigs: [...prev.aiConfigs, aiConfig],
      currentAiConfig: prev.currentAiConfig || configId
    }))

    setNewConfig({
      name: '',
      provider: 'openai',
      apiKey: '',
      model: '',
      baseUrl: ''
    })
    setShowAddConfig(false)

    toast({
      title: '添加成功',
      description: `已添加 ${newConfig.name} 配置`
    })
  }

  const removeAiConfig = (configId: string): void => {
    const config = settings.aiConfigs.find((c) => c.id === configId)
    setSettings((prev) => ({
      ...prev,
      aiConfigs: prev.aiConfigs.filter((c) => c.id !== configId),
      currentAiConfig:
        prev.currentAiConfig === configId
          ? prev.aiConfigs.find((c) => c.id !== configId)?.id || ''
          : prev.currentAiConfig
    }))

    toast({
      title: '删除成功',
      description: `已删除 ${config?.name} 配置`
    })
  }

  const handleProviderChange = (provider: string): void => {
    const template = providerTemplates[provider]
    setNewConfig((prev) => ({
      ...prev,
      provider,
      model: template.defaultModel,
      baseUrl: template.baseUrl
    }))
  }

  return (
    <div className="flex flex-col w-full h-full bg-gradient-to-br from-orange-50/30 to-amber-50/30">
      <header className="sticky top-0 z-10 flex items-center gap-4 px-6 py-4 border-b border-orange-100 bg-white/80 backdrop-blur-sm">
        <SidebarTrigger />
        <div>
          <h1 className="text-2xl font-semibold text-gray-800">设置</h1>
          <p className="text-sm text-gray-600">配置你的 EchoSoul 应用</p>
        </div>
      </header>

      <main className="flex-1 p-6 overflow-auto">
        <div className="max-w-4xl mx-auto">
          <Tabs defaultValue="environment" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="environment">环境配置</TabsTrigger>
              <TabsTrigger value="ai">AI 服务</TabsTrigger>
              <TabsTrigger value="privacy">隐私安全</TabsTrigger>
              <TabsTrigger value="general">通用设置</TabsTrigger>
            </TabsList>

            <TabsContent value="environment">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6"
              >
                <Card className="border-blue-200 bg-gradient-to-br from-blue-50/50 to-indigo-50/50">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-blue-800">
                      <Database className="w-5 h-5" />
                      Chatlog 数据配置
                    </CardTitle>
                    <CardDescription>配置微信聊天记录解密数据目录</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="chatlogWorkDir">Chatlog 工作目录</Label>
                      <div className="flex gap-2">
                        <Input
                          id="chatlogWorkDir"
                          value={settings.chatlogWorkDir}
                          onChange={(e) =>
                            setSettings((prev) => ({ ...prev, chatlogWorkDir: e.target.value }))
                          }
                          placeholder="选择解密后的微信数据目录路径"
                        />
                        <Button variant="outline" onClick={() => testConnection('chatlog')}>
                          浏览
                        </Button>
                      </div>
                    </div>

                    <div className="p-4 rounded-lg bg-blue-100/50">
                      <h4 className="mb-2 font-medium text-blue-800">连接状态</h4>
                      <div className="flex items-center gap-2">
                        <Badge className="text-green-700 bg-green-100">已配置</Badge>
                        <span className="text-sm text-blue-700">
                          成功找到解密数据目录，可以读取聊天记录
                        </span>
                      </div>
                    </div>

                    <div className="p-4 rounded-lg bg-amber-100/50">
                      <h4 className="mb-2 font-medium text-amber-800">使用说明</h4>
                      <ul className="space-y-1 text-sm text-amber-700">
                        <li>• 请先使用微信数据解密工具解密聊天记录</li>
                        <li>• 选择包含解密后数据文件的目录</li>
                        <li>• 确保目录中包含聊天记录数据库文件</li>
                        <li>• 所有数据处理均在本地进行，保护隐私安全</li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>

            <TabsContent value="ai">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6"
              >
                <Card className="border-purple-200 bg-gradient-to-br from-purple-50/50 to-pink-50/50">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-purple-800">
                      <Brain className="w-5 h-5" />
                      AI 服务配置
                    </CardTitle>
                    <CardDescription>自定义添加 AI 服务商配置，支持多模型管理</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {settings.aiConfigs.length > 0 && (
                      <div>
                        <Label htmlFor="currentConfig">默认模型</Label>
                        <Select
                          value={settings.currentAiConfig}
                          onValueChange={switchCurrentConfig}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="选择配置" />
                          </SelectTrigger>
                          <SelectContent>
                            {settings.aiConfigs
                              .filter((config) => config.enabled)
                              .map((config) => (
                                <SelectItem key={config.id} value={config.id}>
                                  {config.name} ({providerTemplates[config.provider]?.name})
                                </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}

                    <div className="flex items-center justify-between">
                      <h4 className="font-medium text-purple-800">AI 配置列表</h4>
                      <Button
                        onClick={() => setShowAddConfig(true)}
                        className="text-white bg-purple-600 hover:bg-purple-700"
                      >
                        添加配置
                      </Button>
                    </div>

                    {showAddConfig && (
                      <Card className="border-blue-200 bg-blue-50/30">
                        <CardHeader className="pb-3">
                          <CardTitle className="text-base">添加新的 AI 配置</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div>
                            <Label htmlFor="configName">配置名称</Label>
                            <Input
                              id="configName"
                              value={newConfig.name}
                              onChange={(e) =>
                                setNewConfig((prev) => ({ ...prev, name: e.target.value }))
                              }
                              placeholder="例如：GPT-4 生产环境"
                            />
                          </div>

                          <div>
                            <Label htmlFor="provider">服务商</Label>
                            <Select value={newConfig.provider} onValueChange={handleProviderChange}>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {Object.entries(providerTemplates).map(([key, template]) => (
                                  <SelectItem key={key} value={key}>
                                    {template.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>

                          <div>
                            <Label htmlFor="apiKey">API Key</Label>
                            <Input
                              id="apiKey"
                              type="password"
                              value={newConfig.apiKey}
                              onChange={(e) =>
                                setNewConfig((prev) => ({ ...prev, apiKey: e.target.value }))
                              }
                              placeholder="输入 API Key"
                            />
                          </div>

                          <div>
                            <Label htmlFor="model">模型</Label>
                            <Input
                              id="model"
                              value={newConfig.model}
                              onChange={(e) =>
                                setNewConfig((prev) => ({ ...prev, model: e.target.value }))
                              }
                              placeholder={`默认：${providerTemplates[newConfig.provider]?.defaultModel}`}
                            />
                          </div>

                          {providerTemplates[newConfig.provider]?.requiresBaseUrl && (
                            <div>
                              <Label htmlFor="baseUrl">API 地址</Label>
                              <Input
                                id="baseUrl"
                                value={newConfig.baseUrl}
                                onChange={(e) =>
                                  setNewConfig((prev) => ({ ...prev, baseUrl: e.target.value }))
                                }
                                placeholder={providerTemplates[newConfig.provider]?.baseUrl}
                              />
                            </div>
                          )}

                          <div className="flex gap-2">
                            <Button onClick={addAiConfig} className="flex-1">
                              添加
                            </Button>
                            <Button
                              variant="outline"
                              onClick={() => setShowAddConfig(false)}
                              className="flex-1"
                            >
                              取消
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    <div className="space-y-4">
                      {settings.aiConfigs.map((config) => (
                        <Card
                          key={config.id}
                          className={`border ${config.enabled ? 'border-green-200 bg-green-50/30' : 'border-gray-200'}`}
                        >
                          <CardHeader className="pb-3">
                            <div className="flex items-center justify-between">
                              <div>
                                <CardTitle className="text-base">{config.name}</CardTitle>
                                <p className="text-sm text-gray-600">
                                  {providerTemplates[config.provider]?.name} - {config.model}
                                </p>
                              </div>
                              <div className="flex items-center gap-2">
                                <Badge variant={config.enabled ? 'default' : 'secondary'}>
                                  {config.enabled ? '已启用' : '已禁用'}
                                </Badge>
                                <Switch
                                  checked={config.enabled}
                                  onCheckedChange={(checked) =>
                                    updateAiConfig(config.id, 'enabled', checked)
                                  }
                                />
                                <Button
                                  variant="destructive"
                                  size="sm"
                                  onClick={() => removeAiConfig(config.id)}
                                >
                                  删除
                                </Button>
                              </div>
                            </div>
                          </CardHeader>
                          <CardContent className="space-y-3">
                            <div>
                              <Label htmlFor={`${config.id}-apiKey`}>API Key</Label>
                              <div className="flex gap-2">
                                <Input
                                  id={`${config.id}-apiKey`}
                                  type="password"
                                  value={config.apiKey}
                                  onChange={(e) =>
                                    updateAiConfig(config.id, 'apiKey', e.target.value)
                                  }
                                  placeholder="输入 API Key"
                                  disabled={!config.enabled}
                                />
                                <Button
                                  variant="outline"
                                  onClick={() => testConnection('ai', config.id)}
                                  disabled={!config.enabled || !config.apiKey}
                                >
                                  测试
                                </Button>
                              </div>
                            </div>

                            <div>
                              <Label htmlFor={`${config.id}-model`}>模型</Label>
                              <Input
                                id={`${config.id}-model`}
                                value={config.model}
                                onChange={(e) => updateAiConfig(config.id, 'model', e.target.value)}
                                placeholder="模型名称"
                                disabled={!config.enabled}
                              />
                            </div>

                            {providerTemplates[config.provider]?.requiresBaseUrl && (
                              <div>
                                <Label htmlFor={`${config.id}-baseUrl`}>API 地址</Label>
                                <Input
                                  id={`${config.id}-baseUrl`}
                                  value={config.baseUrl}
                                  onChange={(e) =>
                                    updateAiConfig(config.id, 'baseUrl', e.target.value)
                                  }
                                  placeholder="API 基础地址"
                                  disabled={!config.enabled}
                                />
                              </div>
                            )}
                          </CardContent>
                        </Card>
                      ))}

                      {settings.aiConfigs.length === 0 && (
                        <div className="py-8 text-center text-gray-500">
                          <p>暂无 AI 配置</p>
                          <p className="text-sm">点击&ldquo;添加配置&rdquo;按钮开始添加</p>
                        </div>
                      )}
                    </div>

                    <div className="p-4 rounded-lg bg-purple-100/50">
                      <h4 className="mb-2 font-medium text-purple-800">使用说明</h4>
                      <ul className="space-y-1 text-sm text-purple-700">
                        <li>• 可以为同一个服务商添加多个不同的配置（不同模型、不同环境等）</li>
                        <li>• 通过顶部下拉框快速切换当前使用的配置</li>
                        <li>• 只有启用的配置才能被选择使用</li>
                        <li>• 支持自定义服务商，可配置任意兼容 OpenAI API 的服务</li>
                      </ul>
                    </div>

                    <div className="p-4 rounded-lg bg-green-100/50">
                      <h4 className="mb-2 font-medium text-green-800">安全提醒</h4>
                      <p className="text-sm text-green-700">
                        所有 API Key 将安全存储在本地，不会上传到任何服务器。
                        所有聊天数据的分析都在你的设备上进行处理。
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>

            <TabsContent value="privacy">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6"
              >
                <Card className="border-green-200 bg-gradient-to-br from-green-50/50 to-emerald-50/50">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-green-800">
                      <Shield className="w-5 h-5" />
                      隐私与安全
                    </CardTitle>
                    <CardDescription>管理你的数据隐私和安全设置</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-base font-medium">自动备份报告</Label>
                        <p className="text-sm text-gray-600">定期备份你的分析报告到本地</p>
                      </div>
                      <Switch
                        checked={settings.autoBackup}
                        onCheckedChange={(checked) =>
                          setSettings((prev) => ({ ...prev, autoBackup: checked }))
                        }
                      />
                    </div>

                    <div className="p-4 rounded-lg bg-green-100/50">
                      <h4 className="mb-3 font-medium text-green-800">数据处理原则</h4>
                      <ul className="space-y-2 text-sm text-green-700">
                        <li>✓ 所有聊天数据仅在本地处理，不上传到云端</li>
                        <li>✓ API Key 使用系统级加密存储</li>
                        <li>✓ 分析报告可选择性备份和导出</li>
                        <li>✓ 支持一键清除所有本地数据</li>
                      </ul>
                    </div>

                    <div className="flex gap-4">
                      <Button variant="outline" className="flex-1">
                        导出所有数据
                      </Button>
                      <Button variant="destructive" className="flex-1">
                        清除所有数据
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>

            <TabsContent value="general">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6"
              >
                <Card className="border-orange-200 bg-gradient-to-br from-orange-50/50 to-amber-50/50">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-orange-800">
                      <SettingsIcon className="w-5 h-5" />
                      通用设置
                    </CardTitle>
                    <CardDescription>个性化你的应用体验</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-base font-medium">桌面通知</Label>
                        <p className="text-sm text-gray-600">报告生成完成时显示通知</p>
                      </div>
                      <Switch
                        checked={settings.notifications}
                        onCheckedChange={(checked) =>
                          setSettings((prev) => ({ ...prev, notifications: checked }))
                        }
                      />
                    </div>

                    <div>
                      <Label htmlFor="theme">界面主题</Label>
                      <Select
                        value={settings.theme}
                        onValueChange={(value) =>
                          setSettings((prev) => ({ ...prev, theme: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="warm">温暖橙色（推荐）</SelectItem>
                          <SelectItem value="cool">清新蓝色</SelectItem>
                          <SelectItem value="nature">自然绿色</SelectItem>
                          <SelectItem value="elegant">优雅紫色</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="p-4 rounded-lg bg-orange-100/50">
                      <h4 className="mb-2 font-medium text-orange-800">关于 EchoSoul</h4>
                      <div className="space-y-1 text-sm text-orange-700">
                        <p>版本：1.0.0 MVP</p>
                        <p>构建时间：2024年12月</p>
                        <p>用AI把微信聊天变成个性化洞察</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>
          </Tabs>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="flex justify-center mt-8"
          >
            <Button
              onClick={handleSave}
              className="px-8 text-white bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600"
            >
              保存所有设置
            </Button>
          </motion.div>
        </div>
      </main>
    </div>
  )
}

export default Settings
